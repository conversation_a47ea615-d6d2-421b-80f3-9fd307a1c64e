"""
UI Module - User interface components
"""

import bpy
from . import main_panel
from . import agent_panel

# List of modules to register
modules = [
    main_panel,
    agent_panel,
]

def register():
    """Register all UI components"""
    for module in modules:
        if hasattr(module, 'register'):
            module.register()

def unregister():
    """Unregister all UI components"""
    for module in reversed(modules):
        if hasattr(module, 'unregister'):
            module.unregister()
