import bpy
from bpy.props import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ty, IntProperty, EnumProperty
from bpy.types import PropertyGroup, AddonPreferences

class AIAgentSettings(PropertyGroup):
    """Main settings for AI Agent system"""
    
    api_key: StringProperty(
        name="OpenAI API Key",
        description="API key for OpenAI services",
        default="",
        subtype='PASSWORD'
    )
    
    crewai_enabled: <PERSON><PERSON><PERSON><PERSON><PERSON>(
        name="Enable Crew AI",
        description="Enable Crew AI integration",
        default=True
    )
    
    mcp_enabled: BoolProperty(
        name="Enable MCP",
        description="Enable Model Context Protocol",
        default=True
    )
    
    log_level: EnumProperty(
        name="Log Level",
        description="Logging verbosity level",
        items=[
            ('DEBUG', 'Debug', 'Detailed debug information'),
            ('INFO', 'Info', 'General information'),
            ('WARNING', 'Warning', 'Warning messages'),
            ('ERROR', 'Error', 'Error messages only')
        ],
        default='INFO'
    )
    
    max_agents: IntProperty(
        name="Max Agents",
        description="Maximum number of concurrent agents",
        default=5,
        min=1,
        max=20
    )
    
    timeout_seconds: IntProperty(
        name="Timeout (seconds)",
        description="Request timeout in seconds",
        default=30,
        min=5,
        max=300
    )

class AIAgentAddonPreferences(AddonPreferences):
    """Addon preferences panel"""
    bl_idname = __package__

    auto_install_deps: BoolProperty(
        name="Auto-install Dependencies",
        description="Automatically install required Python packages",
        default=True
    )
    
    venv_path: StringProperty(
        name="Virtual Environment Path",
        description="Path to Python virtual environment",
        default="",
        subtype='DIR_PATH'
    )
    
    def draw(self, context):
        layout = self.layout
        layout.label(text="AI Agent System Settings")
        
        box = layout.box()
        box.label(text="Dependencies", icon='PREFERENCES')
        box.prop(self, "auto_install_deps")
        box.prop(self, "venv_path")
        
        box = layout.box()
        box.label(text="Advanced", icon='SETTINGS')
        box.label(text="Restart Blender after changing these settings")

def register():
    bpy.utils.register_class(AIAgentSettings)
    bpy.utils.register_class(AIAgentAddonPreferences)

def unregister():
    bpy.utils.unregister_class(AIAgentAddonPreferences)
    bpy.utils.unregister_class(AIAgentSettings)
