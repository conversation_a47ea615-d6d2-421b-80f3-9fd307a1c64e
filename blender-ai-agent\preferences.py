"""
Preferences - User preferences and settings for AI Agent System
"""

import bpy
from bpy.props import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roperty, IntProperty, EnumProperty
from bpy.types import PropertyGroup, AddonPreferences
from .core.config_manager import get_config_manager

class AIAgentSettings(PropertyGroup):
    """Main settings for AI Agent system"""
    
    api_key: StringProperty(
        name="OpenAI API Key",
        description="API key for OpenAI services",
        default="",
        subtype='PASSWORD'
    )
    
    crewai_enabled: BoolProperty(
        name="Enable Crew AI",
        description="Enable Crew AI integration",
        default=True
    )
    
    mcp_enabled: BoolProperty(
        name="Enable MCP",
        description="Enable Model Context Protocol",
        default=True
    )
    
    log_level: EnumProperty(
        name="Log Level",
        description="Logging verbosity level",
        items=[
            ('DEBUG', 'Debug', 'Detailed debug information'),
            ('INFO', 'Info', 'General information'),
            ('WARNING', 'Warning', 'Warning messages'),
            ('ERROR', 'Error', 'Error messages only')
        ],
        default='INFO'
    )
    
    max_agents: IntProperty(
        name="Max Agents",
        description="Maximum number of concurrent agents",
        default=5,
        min=1,
        max=20
    )
    
    timeout_seconds: IntProperty(
        name="Timeout (seconds)",
        description="Request timeout in seconds",
        default=30,
        min=5,
        max=300
    )

class AIAgentAddonPreferences(AddonPreferences):
    """Addon preferences panel"""
    bl_idname = __package__

    auto_install_deps: BoolProperty(
        name="Auto-install Dependencies",
        description="Automatically install required Python packages",
        default=True
    )
    
    venv_path: StringProperty(
        name="Virtual Environment Path",
        description="Path to Python virtual environment",
        default="",
        subtype='DIR_PATH'
    )
    
    def draw(self, context):
        layout = self.layout
        config_manager = get_config_manager()

        # Header
        layout.label(text="AI Agent System Settings", icon='ROBOT')

        # Dependencies section
        box = layout.box()
        box.label(text="Dependencies", icon='PREFERENCES')
        box.prop(self, "auto_install_deps")
        box.prop(self, "venv_path")

        row = box.row()
        row.operator("ai_agent.install_dependencies", text="Install Dependencies", icon='IMPORT')
        row.operator("ai_agent.system_status", text="Check Status", icon='INFO')

        # Configuration section
        box = layout.box()
        box.label(text="Configuration", icon='SETTINGS')

        col = box.column(align=True)
        col.operator("ai_agent.reset_config", text="Reset to Defaults", icon='LOOP_BACK')
        col.operator("ai_agent.export_config", text="Export Config", icon='EXPORT')
        col.operator("ai_agent.import_config", text="Import Config", icon='IMPORT')

        # System info
        box = layout.box()
        box.label(text="System Information", icon='INFO')

        col = box.column(align=True)
        col.label(text=f"Blender Version: {bpy.app.version_string}")
        col.label(text=f"Python Version: {bpy.app.version_string}")

        # Advanced settings note
        box = layout.box()
        box.label(text="Advanced Settings", icon='SETTINGS')
        box.label(text="Use the AI Agents panel in 3D Viewport for detailed configuration")
        box.label(text="Restart Blender after changing critical settings")

def register():
    bpy.utils.register_class(AIAgentSettings)
    bpy.utils.register_class(AIAgentAddonPreferences)

def unregister():
    bpy.utils.unregister_class(AIAgentAddonPreferences)
    bpy.utils.unregister_class(AIAgentSettings)
