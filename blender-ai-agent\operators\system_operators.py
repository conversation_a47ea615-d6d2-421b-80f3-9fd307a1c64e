import bpy
import subprocess
import sys
from pathlib import Path

class AI_OT_InstallDependencies(bpy.types.Operator):
    """Install required Python dependencies"""
    bl_idname = "ai.install_dependencies"
    bl_label = "Install Dependencies"
    bl_description = "Install required Python packages for AI Agent system"
    bl_options = {'REGISTER', 'UNDO'}
    
    def execute(self, context):
        try:
            # Get Python executable path
            python_exe = sys.executable
            
            # Define required packages
            packages = [
                "crewai>=0.30.0",
                "pydantic>=2.0.0",
                "httpx>=0.25.0",
                "websockets>=11.0.0",
                "openai>=1.0.0"
            ]
            
            # Install packages
            for package in packages:
                subprocess.check_call([
                    python_exe, "-m", "pip", "install", package
                ])
            
            self.report({'INFO'}, "Dependencies installed successfully")
            return {'FINISHED'}
            
        except Exception as e:
            self.report({'ERROR'}, f"Failed to install dependencies: {str(e)}")
            return {'CANCELLED'}

class AI_OT_CheckSystem(bpy.types.Operator):
    """Check system requirements and dependencies"""
    bl_idname = "ai.check_system"
    bl_label = "Check System"
    bl_description = "Verify system requirements and dependencies"
    bl_options = {'REGISTER'}
    
    def execute(self, context):
        try:
            # Check Python version
            py_version = sys.version_info
            if py_version < (3, 8):
                self.report({'ERROR'}, "Python 3.8+ required")
                return {'CANCELLED'}
            
            # Check if packages are installed
            required_packages = ['crewai', 'pydantic', 'httpx', 'openai']
            missing = []
            
            for package in required_packages:
                try:
                    __import__(package)
                except ImportError:
                    missing.append(package)
            
            if missing:
                self.report({'WARNING'}, f"Missing packages: {', '.join(missing)}")
            else:
                self.report({'INFO'}, "All dependencies are installed")
            
            return {'FINISHED'}
            
        except Exception as e:
            self.report({'ERROR'}, f"System check failed: {str(e)}")
            return {'CANCELLED'}

class AI_OT_OpenDocumentation(bpy.types.Operator):
    """Open documentation in web browser"""
    bl_idname = "ai.open_documentation"
    bl_label = "Open Documentation"
    bl_description = "Open AI Agent system documentation"
    bl_options = {'REGISTER'}
    
    def execute(self, context):
        import webbrowser
        webbrowser.open("https://github.com/blender-ai-agent/docs")
        return {'FINISHED'}

classes = [
    AI_OT_InstallDependencies,
    AI_OT_CheckSystem,
    AI_OT_OpenDocumentation,
]

def register():
    for cls in classes:
        bpy.utils.register_class(cls)

def unregister():
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
