"""
MCP Panel - UI for Model Context Protocol server management and tool access
"""

import bpy
from bpy.types import Panel, PropertyGroup
from bpy.props import String<PERSON>roperty, EnumProperty, BoolProperty

from ..mcp_integration.server_manager import get_server_manager
from ..mcp_integration.mcp_client import get_mcp_client, ConnectionStatus
from ..mcp_integration.tool_registry import get_tool_registry, ToolCategory

class AI_AGENT_PT_mcp_panel(Panel):
    """MCP server management panel"""
    bl_label = "MCP Servers"
    bl_idname = "AI_AGENT_PT_mcp_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "AI Agents"
    bl_parent_id = "AI_AGENT_PT_main_panel"
    
    def draw(self, context):
        layout = self.layout
        server_manager = get_server_manager()
        mcp_client = get_mcp_client()
        
        # Server Templates section
        box = layout.box()
        box.label(text="Server Templates", icon='NETWORK_DRIVE')
        
        templates = server_manager.list_templates()
        
        col = box.column(align=True)
        for template_name in templates[:5]:  # Show first 5 templates
            template = server_manager.get_template(template_name)
            if template:
                row = col.row()
                row.label(text=f"• {template.name}", icon='PLUGIN')
                add_op = row.operator("ai_agent.add_mcp_server_from_template", text="Add", icon='ADD')
                add_op.template_name = template_name
        
        if len(templates) > 5:
            col.label(text=f"... and {len(templates) - 5} more")
        
        # Quick Actions
        row = box.row()
        row.operator("ai_agent.browse_mcp_templates", text="Browse All Templates", icon='VIEWZOOM')
        
        # Connected Servers section
        box = layout.box()
        box.label(text="Connected Servers", icon='LINKED')
        
        connected_servers = mcp_client.list_connected_servers()
        
        if not connected_servers:
            box.label(text="No servers connected", icon='INFO')
        else:
            col = box.column(align=True)
            for server_name in connected_servers:
                server_info = mcp_client.get_server_info(server_name)
                if server_info:
                    row = col.row()
                    
                    # Status icon
                    status_icon = {
                        'connected': 'CHECKMARK',
                        'connecting': 'TIME',
                        'disconnected': 'CANCEL',
                        'error': 'ERROR'
                    }.get(server_info['status'], 'QUESTION')
                    
                    row.label(text=server_name, icon=status_icon)
                    
                    # Actions
                    disconnect_op = row.operator("ai_agent.disconnect_mcp_server", text="", icon='UNLINKED')
                    disconnect_op.server_name = server_name
                    
                    info_op = row.operator("ai_agent.show_mcp_server_info", text="", icon='INFO')
                    info_op.server_name = server_name
        
        # Connection Actions
        row = box.row()
        row.operator("ai_agent.connect_all_mcp_servers", text="Connect All", icon='LINKED')
        row.operator("ai_agent.refresh_mcp_servers", text="Refresh", icon='FILE_REFRESH')

class AI_AGENT_PT_mcp_tools_panel(Panel):
    """MCP tools panel"""
    bl_label = "MCP Tools"
    bl_idname = "AI_AGENT_PT_mcp_tools_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "AI Agents"
    bl_parent_id = "AI_AGENT_PT_mcp_panel"
    bl_options = {'DEFAULT_CLOSED'}
    
    def draw(self, context):
        layout = self.layout
        tool_registry = get_tool_registry()
        
        # Tool Categories
        tools_by_category = tool_registry.get_tools_by_category()
        
        for category_name, tools in tools_by_category.items():
            if not tools:
                continue
                
            box = layout.box()
            
            # Category header
            category_display = category_name.replace('_', ' ').title()
            box.label(text=f"{category_display} ({len(tools)})", icon='TOOL_SETTINGS')
            
            # Show first few tools
            col = box.column(align=True)
            for tool_name in tools[:3]:  # Show first 3 tools per category
                tool_info = tool_registry.get_tool_info(tool_name)
                if tool_info:
                    row = col.row()
                    row.label(text=f"• {tool_name}")
                    
                    execute_op = row.operator("ai_agent.execute_mcp_tool", text="", icon='PLAY')
                    execute_op.tool_name = tool_name
            
            if len(tools) > 3:
                col.label(text=f"... and {len(tools) - 3} more")
        
        # Tool Statistics
        box = layout.box()
        box.label(text="Tool Statistics", icon='GRAPH')
        
        stats = tool_registry.get_tool_statistics()
        col = box.column(align=True)
        col.label(text=f"Total Tools: {stats['total_tools']}")
        col.label(text=f"Used Tools: {stats['used_tools']}")
        col.label(text=f"Total Executions: {stats['total_executions']}")

class AI_AGENT_PT_mcp_context_panel(Panel):
    """MCP context panel"""
    bl_label = "Blender Context"
    bl_idname = "AI_AGENT_PT_mcp_context_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "AI Agents"
    bl_parent_id = "AI_AGENT_PT_mcp_panel"
    bl_options = {'DEFAULT_CLOSED'}
    
    def draw(self, context):
        layout = self.layout
        
        from ..mcp_integration.context_provider import get_context_provider
        context_provider = get_context_provider()
        
        # Context Summary
        box = layout.box()
        box.label(text="Current Context", icon='SCENE_DATA')
        
        try:
            summary = context_provider.get_context_summary()
            
            col = box.column(align=True)
            col.label(text=f"Scene: {summary['scene']['name']}")
            col.label(text=f"Objects: {summary['statistics']['total_objects']}")
            col.label(text=f"Selected: {summary['selection']['selection_count']}")
            col.label(text=f"Mode: {summary['mode']}")
            
        except Exception as e:
            box.label(text=f"Error: {e}", icon='ERROR')
        
        # Context Actions
        row = box.row()
        row.operator("ai_agent.refresh_context", text="Refresh", icon='FILE_REFRESH')
        row.operator("ai_agent.export_context", text="Export", icon='EXPORT')
        
        # Available Resources
        box = layout.box()
        box.label(text="Available Resources", icon='OUTLINER')
        
        try:
            resources = context_provider.get_available_resources()
            
            col = box.column(align=True)
            for resource_type, items in resources.items():
                if items:
                    col.label(text=f"{resource_type.title()}: {len(items)}")
                    
        except Exception as e:
            box.label(text=f"Error: {e}", icon='ERROR')

# Property groups for MCP configuration
class MCPServerProperties(PropertyGroup):
    """Properties for MCP server configuration"""
    
    server_name: StringProperty(
        name="Server Name",
        description="Name for the MCP server",
        default="My Server"
    )
    
    template_name: StringProperty(
        name="Template",
        description="Template to use for server creation",
        default=""
    )
    
    transport_type: EnumProperty(
        name="Transport",
        description="Transport type for MCP connection",
        items=[
            ('stdio', 'STDIO', 'Standard input/output transport'),
            ('http', 'HTTP', 'HTTP transport'),
            ('streamable_http', 'Streamable HTTP', 'Streamable HTTP transport')
        ],
        default='stdio'
    )
    
    command: StringProperty(
        name="Command",
        description="Command to execute for STDIO transport",
        default=""
    )
    
    url: StringProperty(
        name="URL",
        description="URL for HTTP transport",
        default=""
    )
    
    auto_connect: BoolProperty(
        name="Auto Connect",
        description="Automatically connect when addon starts",
        default=True
    )

class MCPToolProperties(PropertyGroup):
    """Properties for MCP tool execution"""
    
    tool_name: StringProperty(
        name="Tool Name",
        description="Name of the tool to execute",
        default=""
    )
    
    arguments_json: StringProperty(
        name="Arguments (JSON)",
        description="Tool arguments in JSON format",
        default="{}"
    )

# Classes to register
classes = [
    MCPServerProperties,
    MCPToolProperties,
    AI_AGENT_PT_mcp_panel,
    AI_AGENT_PT_mcp_tools_panel,
    AI_AGENT_PT_mcp_context_panel,
]

def register():
    """Register MCP panel classes"""
    for cls in classes:
        bpy.utils.register_class(cls)
    
    # Add properties to scene
    bpy.types.Scene.mcp_server_props = bpy.props.PointerProperty(type=MCPServerProperties)
    bpy.types.Scene.mcp_tool_props = bpy.props.PointerProperty(type=MCPToolProperties)

def unregister():
    """Unregister MCP panel classes"""
    # Remove properties
    if hasattr(bpy.types.Scene, 'mcp_server_props'):
        del bpy.types.Scene.mcp_server_props
    if hasattr(bpy.types.Scene, 'mcp_tool_props'):
        del bpy.types.Scene.mcp_tool_props
    
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
