# AI Agent System for Blender

A comprehensive AI agent system for Blender 4.4+ that integrates Crew AI framework with Model Context Protocol (MCP) servers, providing a user-friendly interface for automated 3D workflows.

## Features

- **Multi-Agent Workflows**: Create teams of specialized AI agents
- **Crew AI Integration**: Leverage the power of collaborative AI agents
- **MCP Server Support**: Connect to external tools and services via Model Context Protocol
- **Blender Native Tools**: Comprehensive toolkit for 3D operations
- **User-Friendly Interface**: Intuitive panels and controls
- **Extensible Architecture**: Plugin system for custom tools

## Requirements

- Blender 4.4.0 or higher
- Python 3.8+ (included with Blender)
- Internet connection for AI services

## Installation

1. Download the addon zip file
2. In Blender, go to Edit > Preferences > Add-ons
3. Click "Install..." and select the zip file
4. Enable "AI Agent System" in the addon list
5. The system will automatically install required dependencies

## Quick Start

1. Open Blender and navigate to the 3D Viewport
2. In the sidebar (N-panel), find the "AI Agents" tab
3. Click "Install Dependencies" if prompted
4. Start creating your first AI agent!

## Development Status

This addon is currently in active development:

- ✅ **Phase 1**: Foundation & Infrastructure (Current)
- 🔄 **Phase 2**: Crew AI Integration (Next)
- ⏳ **Phase 3**: MCP Integration
- ⏳ **Phase 4**: Advanced Features & UI
- ⏳ **Phase 5**: Testing & Polish

## Support

- GitHub Issues: [Report bugs and request features](https://github.com/inkbytefo/blender-ai-agent/issues)
- Documentation: [User Guide](docs/user_guide.md)

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Author

Created by **inkbytefo**
