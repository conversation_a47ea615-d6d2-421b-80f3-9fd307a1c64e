"""
Operators Module - Blender operators for AI Agent System
"""

import bpy
from . import system_operators
from . import agent_operators
from . import mcp_operators

# List of operator modules
modules = [
    system_operators,
    agent_operators,
    mcp_operators,
]

def register():
    """Register all operators"""
    for module in modules:
        if hasattr(module, 'register'):
            module.register()

def unregister():
    """Unregister all operators"""
    for module in reversed(modules):
        if hasattr(module, 'unregister'):
            module.unregister()
