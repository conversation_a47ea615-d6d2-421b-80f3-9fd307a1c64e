import bpy
from bpy.types import Panel

class AI_PT_MainPanel(Panel):
    """Main AI Agent System panel"""
    bl_label = "AI Agent System"
    bl_idname = "AI_PT_main_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "AI Agents"
    
    def draw(self, context):
        layout = self.layout
        settings = context.scene.ai_agent_settings
        
        # System status
        box = layout.box()
        box.label(text="System Status", icon='INFO')
        
        # Check dependencies
        try:
            import crewai
            box.label(text="Crew AI: ✓ Installed", icon='CHECKMARK')
        except ImportError:
            box.label(text="Crew AI: ✗ Missing", icon='ERROR')
            box.operator("ai.install_dependencies", icon='IMPORT')
        
        try:
            import openai
            box.label(text="OpenAI: ✓ Installed", icon='CHECKMARK')
        except ImportError:
            box.label(text="OpenAI: ✗ Missing", icon='ERROR')
        
        # Settings
        box = layout.box()
        box.label(text="Settings", icon='PREFERENCES')
        box.prop(settings, "api_key")
        box.prop(settings, "max_agents")
        box.prop(settings, "timeout_seconds")
        
        # Quick actions
        box = layout.box()
        box.label(text="Quick Actions", icon='PLAY')
        row = box.row(align=True)
        row.operator("ai.check_system", icon='VIEWZOOM')
        row.operator("ai.open_documentation", icon='HELP')

class AI_PT_SystemPanel(Panel):
    """System management panel"""
    bl_label = "System Management"
    bl_idname = "AI_PT_system_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "AI Agents"
    bl_parent_id = "AI_PT_main_panel"
    
    def draw(self, context):
        layout = self.layout
        
        col = layout.column(align=True)
        col.operator("ai.install_dependencies", icon='IMPORT')
        col.operator("ai.check_system", icon='VIEWZOOM')
        
        # System info
        box = layout.box()
        box.label(text="System Info", icon='SYSTEM')
        
        import sys
        box.label(text=f"Python: {sys.version.split()[0]}")
        box.label(text=f"Platform: {sys.platform}")

classes = [
    AI_PT_MainPanel,
    AI_PT_SystemPanel,
]

def register():
    for cls in classes:
        bpy.utils.register_class(cls)

def unregister():
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
