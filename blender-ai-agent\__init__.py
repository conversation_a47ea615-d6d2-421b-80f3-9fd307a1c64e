bl_info = {
    "name": "AI Agent System",
    "author": "Blender AI Team",
    "version": (1, 0, 0),
    "blender": (4, 4, 0),
    "location": "View3D > Sidebar > AI Agents",
    "description": "Crew AI powered agent system for Blender",
    "warning": "",
    "doc_url": "",
    "category": "3D View",
}

import bpy
import sys
import os
from pathlib import Path

# Add the addon directory to Python path
addon_dir = Path(__file__).parent
if str(addon_dir) not in sys.path:
    sys.path.insert(0, str(addon_dir))

# Import modules
from . import preferences
from . import operators
from . import panels
from .crew_system import crew_manager
from .mcp_client import connection_manager
from .utils import logging_config

# Reload modules for development
if "bpy" in locals():
    import importlib
    importlib.reload(preferences)
    importlib.reload(operators)
    importlib.reload(panels)
    importlib.reload(crew_manager)
    importlib.reload(connection_manager)
    importlib.reload(logging_config)

def register():
    """Register all classes and properties"""
    logging_config.setup_logging()
    
    preferences.register()
    operators.register()
    panels.register()
    crew_manager.register()
    connection_manager.register()
    
    # Add custom properties to scene
    bpy.types.Scene.ai_agent_settings = bpy.props.PointerProperty(
        type=preferences.AIAgentSettings
    )

def unregister():
    """Unregister all classes and properties"""
    del bpy.types.Scene.ai_agent_settings
    
    connection_manager.unregister()
    crew_manager.unregister()
    panels.unregister()
    operators.unregister()
    preferences.unregister()

if __name__ == "__main__":
    register()
