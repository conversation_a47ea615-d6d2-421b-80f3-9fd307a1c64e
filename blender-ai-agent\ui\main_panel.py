"""
Main Panel - Primary control interface for AI Agent System
"""

import bpy
from bpy.types import Panel
from ..core.addon_manager import get_manager

class AI_AGENT_PT_main_panel(Panel):
    """Main control panel for AI Agent System"""
    bl_label = "AI Agent System"
    bl_idname = "AI_AGENT_PT_main_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "AI Agents"
    
    def draw(self, context):
        layout = self.layout
        scene = context.scene
        addon_manager = get_manager()
        
        # Header
        row = layout.row()
        row.label(text="AI Agent System v1.0", icon='OUTLINER_OB_ARMATURE')
        
        # Status section
        box = layout.box()
        box.label(text="System Status", icon='INFO')
        
        col = box.column(align=True)
        
        # Addon status
        if addon_manager.is_initialized:
            col.label(text="✓ System Initialized", icon='CHECKMARK')
        else:
            col.label(text="✗ System Not Initialized", icon='ERROR')
        
        # Dependencies status
        if addon_manager.dependencies_installed:
            col.label(text="✓ Dependencies Available", icon='CHECKMARK')
        else:
            col.label(text="⚠ Dependencies Missing", icon='ERROR')
            col.operator("ai_agent.install_dependencies", text="Install Dependencies")
        
        # Quick Actions section
        box = layout.box()
        box.label(text="Quick Actions", icon='PLAY')
        
        col = box.column(align=True)
        
        if addon_manager.is_initialized:
            col.operator("ai_agent.create_agent", text="Create New Agent", icon='ARMATURE_DATA')
            col.operator("ai_agent.manage_crews", text="Manage Crews", icon='OUTLINER_COLLECTION')
            col.operator("ai_agent.mcp_servers", text="MCP Servers", icon='NETWORK_DRIVE')
        else:
            col.label(text="Initialize system first")
        
        # Settings section
        box = layout.box()
        box.label(text="Settings", icon='PREFERENCES')
        
        col = box.column(align=True)
        col.operator("ai_agent.open_preferences", text="Open Preferences", icon='SETTINGS')
        col.operator("ai_agent.view_logs", text="View Logs", icon='TEXT')

class AI_AGENT_PT_status_panel(Panel):
    """Status panel showing current operations"""
    bl_label = "Status"
    bl_idname = "AI_AGENT_PT_status_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "AI Agents"
    bl_parent_id = "AI_AGENT_PT_main_panel"
    bl_options = {'DEFAULT_CLOSED'}
    
    def draw(self, context):
        layout = self.layout
        
        # Active agents
        box = layout.box()
        box.label(text="Active Agents", icon='ARMATURE_DATA')
        
        # TODO: Show active agents list
        col = box.column()
        col.label(text="No active agents")
        
        # Recent tasks
        box = layout.box()
        box.label(text="Recent Tasks", icon='SEQUENCE')
        
        # TODO: Show recent tasks
        col = box.column()
        col.label(text="No recent tasks")

# Classes to register
classes = [
    AI_AGENT_PT_main_panel,
    AI_AGENT_PT_status_panel,
]

def register():
    """Register panel classes"""
    for cls in classes:
        bpy.utils.register_class(cls)

def unregister():
    """Unregister panel classes"""
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
